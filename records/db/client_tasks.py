from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_task(values, session=None):
    client_task = models.ClientTask(**values)

    try:
        session.add(client_task)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientTask: %s" % e
        )

    return client_task


@base.session_aware()
async def update_client_task(client_task: models.ClientTask, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientTask).where(models.ClientTask.id == client_task.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_task.update(new_values)

    return client_task


@base.session_aware()
async def delete_client_task(id: int, session=None):
    delete_q = delete(models.ClientTask).where(models.ClientTask.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_tasks(ids: list[int] = None, client_id: str = None, client_service_id: int = None, session=None):
    return await base.delete_client_datas(
        models.ClientTask, ids=ids, client_id=client_id, client_service_id=client_service_id, session=session
    )


@base.session_aware()
async def list_client_tasks(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    # if limit and page:
    #     limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientTask).where(models.ClientTask.client_id == client_id)
    # if q:
    #     query = query.where(models.ClientTask.name.ilike(f'%{q}%'))

    # if not hasattr(models.ClientTask, order):
    #     order = 'id'
    # order_col = getattr(models.ClientTask, order)

    # if desc:
    #     order_col = order_col.desc()
    # query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.scalars().fetchall()
    return res


@base.session_aware()
async def list_client_tasks_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientTask)
    if ids:
        query = query.where(models.ClientTask.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_task_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientTask, id, session=session)


@base.session_aware()
async def get_client_task_count(
    client_id: str = None,
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if client_id:
        where.append('client_id = :client_id')
        params['client_id'] = client_id
    # if q:
    #     q = f'%{q}%'
    #     where.append('name ILIKE :q')
    #     params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_tasks'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
