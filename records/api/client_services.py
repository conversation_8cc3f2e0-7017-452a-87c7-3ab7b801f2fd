import os
import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, field_validator

from records.utils import json_utils

from records.context import context as ctx
from records.db import api as db_api


class ClientServiceCreate(BaseModel):
    service_id: str
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)
    

class ClientServiceUpdate(BaseModel):
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    note: Optional[str] = None
    discount_percent: Optional[int] = None
    discount_amount: Optional[str] = None
    total: Optional[str] = None

    _date_validator = field_validator(
        *['start_date', 'end_date'],
        mode='before'
    )(json_utils.date_validator)


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'services'))

    router.add_api_route("", list_services, methods=['GET'], name='List client services')
    router.add_api_route("", create_client_service, methods=['POST'], name='Create client service')
    router.add_api_route("/{service_id}", get_client_service, methods=['GET'], name='Get client service')
    router.add_api_route("/{service_id}", update_client_service, methods=['PUT'], name='Update client service')
    router.add_api_route("/{service_id}", delete_client_service, methods=['DELETE'], name='Delete client service')

    return router


async def list_services(
    client_id: str,
    # session: AsyncSession = Depends(get_session)
):
    services = await db_api.list_client_services(client_id=client_id)
    return {'items': services}


async def get_client_service(client_id: str, service_id: int):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    return client_service_db


async def create_client_service(client_id: str, service: ClientServiceCreate):

    client_service_dict = service.model_dump(exclude_unset=True)
    client_service_dict['client_id'] = client_id

    db_client = await db_api.create_client_service(client_service_dict)
    return db_client


async def update_client_service(client_id: str, service_id: int, service: ClientServiceUpdate):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    updated_client = await db_api.update_client_service(client_service_db, service.model_dump())
    return updated_client


async def delete_client_service(client_id: str, service_id: int, delete_tasks: bool = False):
    client_service_db = await db_api.get_client_service_by_id(service_id)
    if delete_tasks:
        await db_api.delete_client_tasks(client_id=client_id, client_service_id=service_id)
    await db_api.delete_client_service(service_id)
    return None
